<template>
	<view class="clearance-container">
		<!-- 顶部搜索和位置 -->
		<view class="top-section">
			<!-- 位置选择器 -->
			<view class="location-bar" @click="showCityPicker">
				<view class="location-content">
					<text class="location-icon">📍</text>
					<text class="location-text">{{ currentLocationText }}</text>
					<text class="location-arrow">▼</text>
				</view>
			</view>

			<!-- 页面标题 -->
			<view class="page-title">
				<text class="title-text">🔥 特价清仓区</text>
				<text class="subtitle-text">限时特价，清仓处理</text>
			</view>
		</view>

		<!-- 商品列表 -->
		<scroll-view
			class="product-list"
			scroll-y="true"
			:refresher-enabled="true"
			:refresher-triggered="refreshing"
			@refresherrefresh="onRefresh"
			@scrolltolower="loadMore"
		>
			<view v-if="productList.length > 0">
				<view class="product-item"
					  v-for="(product, index) in productList"
					  :key="product.productId"
					  @click="goToProductDetail(product)">
					<view class="product-image">
						<image :src="getImageUrl(product.productImages)" mode="aspectFill"></image>
						<view class="promotion-tag">促销</view>
					</view>
					<view class="product-info">
						<text class="product-name">{{ product.productName }}</text>
						<view class="store-info">
							<image :src="getImageUrl(product.storeAvatar)" class="store-avatar" mode="aspectFill"></image>
							<text class="store-name">{{ product.storeName }}</text>
						</view>
						<view class="market-address">
							<text class="address-text">{{ product.marketAddress }}</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 空状态 -->
			<view v-else-if="!loading" class="empty-state">
				<text class="empty-icon">🛍️</text>
				<text class="empty-text">暂无促销商品</text>
				<text class="empty-desc">商家还没有设置促销商品</text>
			</view>

			<!-- 加载更多 -->
			<view v-if="loading && productList.length > 0" class="loading-more">
				<text class="loading-text">加载中...</text>
			</view>

			<!-- 没有更多数据 -->
			<view v-if="!hasMore && productList.length > 0" class="no-more">
				<text class="no-more-text">— 已经到底了 —</text>
			</view>
		</scroll-view>

		<!-- 城市选择弹窗 -->
		<u-popup :show="showCityPopup" @close="showCityPopup = false" mode="bottom" height="70%" border-radius="20">
			<view class="city-popup">
				<view class="popup-header">
					<text class="popup-title">选择城市和市场</text>
					<u-icon name="close" @click="closeCityPopup" size="20"></u-icon>
				</view>
				<view class="city-content">
					<view class="city-section">
						<text class="section-title">选择城市</text>
						<view class="city-grid">
							<view
								v-for="city in cities"
								:key="city.id"
								class="city-item"
								:class="{ 'active': currentCityId === city.id }"
								@click="selectCity(city)"
							>
								<text class="city-name">{{ getCityName(city) }}</text>
							</view>
						</view>
					</view>
					<view class="market-section" v-if="currentCityMarkets.length > 0">
						<text class="section-title">选择市场</text>
						<view class="market-list">
							<view
								v-for="market in currentCityMarkets"
								:key="market.id"
								class="market-item"
								:class="{ 'active': currentMarketId === market.id }"
								@click="selectMarket(market)"
							>
								<text class="market-name">{{ getMarketName(market) }}</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</u-popup>

		<!-- 自定义底部导航栏 -->
		<custom-tabbar ref="customTabbar" :current="0" @change="onTabChange"></custom-tabbar>
	</view>
</template>

<script>
import CustomTabbar from '@/components/custom-tabbar/custom-tabbar.vue'
import { listPromotions } from '@/api/buy/product.js'
import { listNodes } from '@/api/buy/nodes.js'
import { baseUrl } from '@/config.js'

export default {
	components: {
		CustomTabbar
	},
	data() {
		return {
			baseUrl,
			loading: false,
			refreshing: false,
			currentCategory: 0,
			hasMore: true,

			// 定位相关
			locationProcessed: false,
			currentCityId: null,
			currentMarketId: null,
			allNodes: [],
			cities: [],
			currentCityMarkets: [],
			showCityPopup: false,

			// 查询参数
			queryParams: {
				pageNum: 1,
				pageSize: 10,
				city: '',
				market: ''
			},

			productList: [],
			total: 0
		}
	},

	computed: {
		// 当前位置显示文本
		currentLocationText() {
			if (!this.currentCityId) {
				return '选择城市';
			}

			const city = this.cities.find(c => c.id === this.currentCityId);
			const cityName = this.getCityName(city);

			if (!this.currentMarketId) {
				return cityName;
			}

			const market = this.currentCityMarkets.find(m => m.id === this.currentMarketId);
			const marketName = this.getMarketName(market);

			return `${cityName} - ${marketName}`;
		}
	},
	onLoad() {
		uni.setNavigationBarTitle({
			title: '特价清仓区'
		});
		this.initPage();
	},
	onShow() {
		// 同步tabBar状态
		this.$nextTick(() => {
			if (this.$refs.customTabbar) {
				this.$refs.customTabbar.syncCurrentPageState();
			}
		});
	},
	methods: {
		// 初始化页面
		async initPage() {
			// 先加载节点数据
			await this.fetchNodesData();
			// 数据加载完成后获取用户定位
			this.getUserLocation();

			// 设置超时，如果10秒内没有设置城市，则强制设置默认城市
			setTimeout(() => {
				if (!this.currentCityId && !this.locationProcessed) {
					this.forceSetDefaultCity();
				}
			}, 10000);
		},

		// 获取节点数据
		async fetchNodesData() {
			try {
				this.loading = true;
				const res = await listNodes({pageSize:10000000});
				const flatList = res.rows || [];

				const nodeMap = new Map();
				const tree = [];

				// 构建节点映射
				flatList.forEach(node => {
					nodeMap.set(node.id, { ...node, children: [] });
				});

				// 构建树形结构
				flatList.forEach(node => {
					if (node.parentId && nodeMap.has(node.parentId)) {
						const parent = nodeMap.get(node.parentId);
						parent.children.push(node);
					} else {
						tree.push(node);
					}
				});

				this.allNodes = tree;

				// 先尝试原来的过滤方式
				let cities = tree.filter(node => node.nodeType === 'city');

				// 如果没有找到，尝试其他可能的字段名
				if (cities.length === 0) {
					cities = tree.filter(node =>
						node.type === 'city' ||
						node.level === 1 ||
						node.parentId === 0
					);
				}

				// 进一步过滤确保有名称
				this.cities = cities.filter(node =>
					node.nodeName || node.name || node.cityName
				);

			} catch (error) {
				uni.showToast({ title: '数据加载失败', icon: 'none' });
			} finally {
				this.loading = false;
			}
		},

		// 获取用户定位
		getUserLocation(retryCount = 0) {
			// 如果已经处理过定位，则不再处理
			if (this.locationProcessed) {
				return;
			}

			// 如果城市数据还没加载完成，等待一下再试（最多重试10次）
			if (this.cities.length === 0) {
				if (retryCount < 10) {
					setTimeout(() => {
						this.getUserLocation(retryCount + 1);
					}, 300);
					return;
				} else {
					this.locationProcessed = true;
					return;
				}
			}

			// 先检查定位权限
			uni.getSetting({
				success: (res) => {
					if (res.authSetting['scope.userLocation'] === false) {
						// 用户之前拒绝了定位权限，询问是否重新授权
						uni.showModal({
							title: '定位权限',
							content: '需要获取您的位置信息来推荐附近商品，是否重新授权？',
							success: (modalRes) => {
								if (modalRes.confirm) {
									this.getLocation();
								} else {
									this.setDefaultCity();
								}
							}
						});
					} else {
						// 有权限或未询问过，直接获取定位
						this.getLocation();
					}
				},
				fail: () => {
					this.setDefaultCity();
				}
			});
		},

		// 获取定位
		getLocation() {
			uni.getLocation({
				type: 'gcj02', // 返回可以用于uni.openLocation的经纬度
				success: (res) => {
					this.parseLocationToCity(res.latitude, res.longitude);
				},
				fail: (err) => {
					if (err.errMsg.includes('auth deny')) {
						uni.showToast({ title: '定位权限被拒绝', icon: 'none' });
					}
					this.setDefaultCity();
				}
			});
		},

		// 解析经纬度为城市信息
		parseLocationToCity(latitude, longitude) {
			const key = 'OYKBZ-DAB65-HAOIY-IP4UA-2EG2O-DUBHC'; // 腾讯地图API密钥
			const url = `https://apis.map.qq.com/ws/geocoder/v1/?location=${latitude},${longitude}&key=${key}&get_poi=0`;

			uni.request({
				url: url,
				success: (mapRes) => {
					if (mapRes.data && mapRes.data.status === 0 && mapRes.data.result) {
						const city = mapRes.data.result.address_component.city;
						this.matchCityWithNodes(city);
					} else {
						this.setDefaultCity();
					}
				},
				fail: () => {
					this.setDefaultCity();
				}
			});
		},

		// 匹配城市与nodes数据
		matchCityWithNodes(userCity) {
			if (!userCity || this.cities.length === 0) {
				this.setDefaultCity();
				return;
			}

			// 清理用户城市名称（去掉"市"字和其他后缀）
			const cleanUserCity = this.cleanCityName(userCity);

			// 在cities中查找匹配的城市
			const matchedCity = this.cities.find(city => {
				// 确保city存在
				if (!city) {
					return false;
				}

				// 获取城市名称，尝试多个可能的字段
				const cityName = city.nodeName || city.name || city.cityName;
				if (!cityName) {
					return false;
				}

				// 清理后台城市名称
				const cleanCityName = this.cleanCityName(cityName);

				// 多种匹配方式
				return this.isCityMatch(cleanUserCity, cleanCityName, userCity, cityName);
			});

			if (matchedCity) {
				this.currentCityId = matchedCity.id;
				this.updateCityMarkets();
				this.updateQueryParams();
				this.loadPromotionProducts();
			} else {
				this.setDefaultCity();
			}

			// 标记定位处理完成
			this.locationProcessed = true;
		},

		// 清理城市名称，去掉各种后缀
		cleanCityName(cityName) {
			if (!cityName) return '';

			return cityName
				.replace(/市$/, '')        // 去掉"市"
				.replace(/自治区$/, '')     // 去掉"自治区"
				.replace(/特别行政区$/, '') // 去掉"特别行政区"
				.replace(/地区$/, '')      // 去掉"地区"
				.replace(/盟$/, '')        // 去掉"盟"
				.replace(/州$/, '')        // 去掉"州"
				.trim();
		},

		// 判断两个城市名是否匹配
		isCityMatch(cleanUserCity, cleanCityName, originalUserCity, originalCityName) {
			// 1. 清理后的名称完全匹配
			if (cleanUserCity === cleanCityName) {
				return true;
			}

			// 2. 原始名称完全匹配
			if (originalUserCity === originalCityName) {
				return true;
			}

			// 3. 清理后的名称包含关系（双向）
			if (cleanUserCity.includes(cleanCityName) || cleanCityName.includes(cleanUserCity)) {
				return true;
			}

			// 4. 原始名称包含关系（双向）
			if (originalUserCity.includes(originalCityName) || originalCityName.includes(originalUserCity)) {
				return true;
			}

			return false;
		},

		// 设置默认城市（第一个城市）
		setDefaultCity() {
			if (this.cities.length > 0) {
				this.currentCityId = this.cities[0].id;
				this.updateCityMarkets();
				this.updateQueryParams();
				this.loadPromotionProducts();
			}
			// 标记定位处理完成
			this.locationProcessed = true;
		},

		// 强制设置默认城市（在数据加载完成但没有定位时使用）
		forceSetDefaultCity() {
			if (this.cities.length > 0 && !this.currentCityId) {
				this.currentCityId = this.cities[0].id;
				this.updateCityMarkets();
				this.updateQueryParams();
				this.loadPromotionProducts();
			}
		},

		// 更新当前城市的市场列表
		updateCityMarkets() {
			if (!this.currentCityId) {
				this.currentCityMarkets = [];
				return;
			}

			const currentCity = this.allNodes.find(node => node.id === this.currentCityId);
			if (currentCity && currentCity.children) {
				this.currentCityMarkets = currentCity.children.filter(node =>
					node.nodeType === 'market' || node.type === 'market'
				);
			} else {
				this.currentCityMarkets = [];
			}

			// 如果有市场，默认选择第一个
			if (this.currentCityMarkets.length > 0) {
				this.currentMarketId = this.currentCityMarkets[0].id;
			} else {
				this.currentMarketId = null;
			}
		},

		// 更新查询参数
		updateQueryParams() {
			const city = this.cities.find(c => c.id === this.currentCityId);
			const market = this.currentCityMarkets.find(m => m.id === this.currentMarketId);

			this.queryParams.city = this.getCityName(city) || '';
			this.queryParams.market = this.getMarketName(market) || '';
		},

		// 获取城市名称
		getCityName(city) {
			if (!city) return '';
			return city.nodeName || city.name || city.cityName || '';
		},

		// 获取市场名称
		getMarketName(market) {
			if (!market) return '';
			return market.nodeName || market.name || market.marketName || '';
		},

		// 加载促销商品数据
		async loadPromotionProducts(isRefresh = false) {
			if (this.loading) return;

			try {
				this.loading = true;
				if (isRefresh) {
					this.queryParams.pageNum = 1;
					this.productList = [];
				}

				const res = await listPromotions(this.queryParams);

				if (res.code === 200) {
					const newList = res.rows || [];
					if (isRefresh) {
						this.productList = newList;
					} else {
						this.productList = [...this.productList, ...newList];
					}

					this.total = res.total || 0;
					this.hasMore = this.productList.length < this.total;
				} else {
					uni.showToast({ title: res.msg || '加载失败', icon: 'none' });
				}
			} catch (error) {
				uni.showToast({ title: '网络错误', icon: 'none' });
			} finally {
				this.loading = false;
				this.refreshing = false;
			}
		},

		// 下拉刷新
		onRefresh() {
			this.refreshing = true;
			this.loadPromotionProducts(true);
		},

		// 加载更多
		loadMore() {
			if (!this.hasMore || this.loading) return;

			this.queryParams.pageNum++;
			this.loadPromotionProducts();
		},

		// 显示城市选择器
		showCityPicker() {
			this.showCityPopup = true;
		},

		// 关闭城市选择器
		closeCityPopup() {
			this.showCityPopup = false;
		},

		// 选择城市
		selectCity(city) {
			this.currentCityId = city.id;
			this.updateCityMarkets();
			this.updateQueryParams();
			this.loadPromotionProducts(true);
		},

		// 选择市场
		selectMarket(market) {
			this.currentMarketId = market.id;
			this.updateQueryParams();
			this.loadPromotionProducts(true);
			this.showCityPopup = false;
		},



		// 获取图片URL
		getImageUrl(imagePath) {
			if (!imagePath) return '/static/images/placeholder.jpg';
			if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
				return imagePath;
			}
			// 处理多张图片，取第一张
			const firstImage = imagePath.split(',')[0];
			return this.baseUrl + firstImage;
		},

		// 跳转到商品详情
		goToProductDetail(product) {
			uni.navigateTo({
				url: `/pages/product/detail?id=${product.productId}`
			});
		},

		// tabBar切换事件
		onTabChange() {
			// 自定义tabBar组件内部已经处理了页面跳转
		}
	}
}
</script>

<style lang="scss" scoped>
.clearance-container {
	min-height: 100vh;
	background: linear-gradient(to bottom, #F5C6CB, #ffffff 50%);
	padding-bottom: 120rpx; /* 为自定义tabBar预留空间 */
}

.top-section {
	padding: 40rpx 30rpx 30rpx;

	.location-bar {
		margin-bottom: 30rpx;

		.location-content {
			display: flex;
			align-items: center;
			justify-content: center;
			padding: 16rpx 24rpx;
			background: rgba(255, 255, 255, 0.2);
			border-radius: 50rpx;
			backdrop-filter: blur(10rpx);
			transition: all 0.3s ease;

			&:active {
				transform: scale(0.98);
			}

			.location-icon {
				font-size: 28rpx;
				margin-right: 8rpx;
				color: #fff;
			}

			.location-text {
				font-size: 28rpx;
				color: #fff;
				margin-right: 8rpx;
				font-weight: 500;
			}

			.location-arrow {
				font-size: 20rpx;
				color: rgba(255, 255, 255, 0.8);
			}
		}
	}

	.page-title {
		text-align: center;

		.title-text {
			display: block;
			font-size: 48rpx;
			font-weight: bold;
			color: #fff;
			text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
			margin-bottom: 10rpx;
		}

		.subtitle-text {
			display: block;
			font-size: 28rpx;
			color: rgba(255, 255, 255, 0.9);
		}
	}
}



.product-list {
	flex: 1;
	padding: 0 20rpx;
	background: rgba(255, 255, 255, 0.1);
	border-radius: 30rpx 30rpx 0 0;
	margin-top: 20rpx;
	min-height: 60vh;
}

.product-item {
	display: flex;
	background: #fff;
	border-radius: 16rpx;
	margin-bottom: 20rpx;
	padding: 20rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
	transition: all 0.3s ease;

	&:first-child {
		margin-top: 20rpx;
	}

	&:active {
		transform: scale(0.98);
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);
	}

	.product-image {
		position: relative;
		width: 180rpx;
		height: 180rpx;
		border-radius: 12rpx;
		overflow: hidden;
		margin-right: 20rpx;

		image {
			width: 100%;
			height: 100%;
		}

		.promotion-tag {
			position: absolute;
			top: 8rpx;
			left: 8rpx;
			background: linear-gradient(135deg, #ff6b6b 0%, #fa3534 100%);
			color: #fff;
			padding: 4rpx 12rpx;
			border-radius: 20rpx;
			font-size: 20rpx;
			font-weight: bold;
			box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.3);
		}
	}

	.product-info {
		flex: 1;
		display: flex;
		flex-direction: column;
		justify-content: space-between;

		.product-name {
			font-size: 30rpx;
			font-weight: 600;
			color: #333;
			margin-bottom: 16rpx;
			line-height: 1.4;
			display: -webkit-box;
			-webkit-box-orient: vertical;
			-webkit-line-clamp: 2;
			overflow: hidden;
		}

		.store-info {
			display: flex;
			align-items: center;
			margin-bottom: 12rpx;

			.store-avatar {
				width: 36rpx;
				height: 36rpx;
				border-radius: 50%;
				margin-right: 10rpx;
				border: 2rpx solid #f0f0f0;
			}

			.store-name {
				font-size: 26rpx;
				color: #666;
				font-weight: 500;
			}
		}

		.market-address {
			.address-text {
				font-size: 24rpx;
				color: #999;
				line-height: 1.3;
				display: -webkit-box;
				-webkit-box-orient: vertical;
				-webkit-line-clamp: 1;
				overflow: hidden;
			}
		}
	}
}

.empty-state {
	text-align: center;
	padding: 100rpx 40rpx;
	background: #fff;
	margin: 20rpx;
	border-radius: 16rpx;

	.empty-icon {
		font-size: 120rpx;
		margin-bottom: 20rpx;
		display: block;
		opacity: 0.6;
	}

	.empty-text {
		font-size: 32rpx;
		color: #666;
		margin-bottom: 12rpx;
		display: block;
		font-weight: 500;
	}

	.empty-desc {
		font-size: 28rpx;
		color: #999;
		display: block;
	}
}

.loading-more, .no-more {
	text-align: center;
	padding: 40rpx;
	color: #999;
	font-size: 28rpx;
	background: #fff;
	margin: 0 20rpx 20rpx;
	border-radius: 16rpx;
}

.loading-more, .no-more {
	text-align: center;
	padding: 40rpx;
	color: #999;
	font-size: 28rpx;
}

.city-popup {
	.popup-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx;
		border-bottom: 1rpx solid #f0f0f0;

		.popup-title {
			font-size: 36rpx;
			font-weight: 600;
			color: #333;
		}
	}

	.city-content {
		padding: 30rpx;
		max-height: 60vh;
		overflow-y: auto;

		.section-title {
			font-size: 32rpx;
			font-weight: 600;
			color: #333;
			margin-bottom: 20rpx;
			display: block;
		}

		.city-grid {
			display: grid;
			grid-template-columns: repeat(3, 1fr);
			gap: 20rpx;
			margin-bottom: 40rpx;

			.city-item {
				padding: 20rpx;
				background: #f8f9fa;
				border-radius: 12rpx;
				text-align: center;
				transition: all 0.3s ease;

				&.active {
					background: #fa3534;
					color: #fff;
				}

				.city-name {
					font-size: 28rpx;
				}
			}
		}

		.market-list {
			.market-item {
				padding: 24rpx;
				background: #f8f9fa;
				border-radius: 12rpx;
				margin-bottom: 16rpx;
				transition: all 0.3s ease;

				&.active {
					background: #fa3534;
					color: #fff;
				}

				.market-name {
					font-size: 30rpx;
				}
			}
		}
	}
}
</style>
