<template>
	<view class="clearance-container">
		<!-- 顶部标题栏 -->
		<view class="header">
			<view class="header-content">
				<text class="title">🔥 特价清仓区</text>
				<text class="subtitle">限时特价，清仓处理</text>
			</view>
		</view>

		<!-- 轮播图区域 -->
		<view class="banner-section">
			<swiper class="banner-swiper" indicator-dots="true" autoplay="true" interval="3000" duration="500">
				<swiper-item>
					<view class="banner-item banner-1">
						<text class="banner-text">清仓大促销</text>
						<text class="banner-desc">全场5折起</text>
					</view>
				</swiper-item>
				<swiper-item>
					<view class="banner-item banner-2">
						<text class="banner-text">限时抢购</text>
						<text class="banner-desc">数量有限</text>
					</view>
				</swiper-item>
				<swiper-item>
					<view class="banner-item banner-3">
						<text class="banner-text">库存清理</text>
						<text class="banner-desc">低价处理</text>
					</view>
				</swiper-item>
			</swiper>
		</view>

		<!-- 分类标签 -->
		<view class="category-tabs">
			<scroll-view class="tabs-scroll" scroll-x="true">
				<view class="tab-item" 
					  v-for="(category, index) in categories" 
					  :key="index"
					  :class="{ 'active': currentCategory === index }"
					  @click="switchCategory(index)">
					<text class="tab-icon">{{ category.icon }}</text>
					<text class="tab-text">{{ category.name }}</text>
				</view>
			</scroll-view>
		</view>

		<!-- 商品列表 -->
		<view class="product-list">
			<view class="product-item" 
				  v-for="(product, index) in productList" 
				  :key="index"
				  @click="goToProductDetail(product)">
				<view class="product-image">
					<image :src="getImageUrl(product.image)" mode="aspectFill"></image>
					<view class="discount-tag">{{ product.discount }}</view>
				</view>
				<view class="product-info">
					<text class="product-name">{{ product.name }}</text>
					<view class="price-section">
						<text class="current-price">¥{{ product.currentPrice }}</text>
						<text class="original-price">¥{{ product.originalPrice }}</text>
					</view>
					<view class="product-tags">
						<text class="tag">{{ product.tag }}</text>
						<text class="stock">仅剩{{ product.stock }}件</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 加载更多 -->
		<view class="load-more" v-if="hasMore">
			<text>加载更多...</text>
		</view>

		<!-- 到底了提示 -->
		<view class="no-more" v-else>
			<text>— 已经到底了 —</text>
		</view>

		<!-- 自定义底部导航栏 -->
		<custom-tabbar ref="customTabbar" :current="0" @change="onTabChange"></custom-tabbar>
	</view>
</template>

<script>
import CustomTabbar from '@/components/custom-tabbar/custom-tabbar.vue'

export default {
	components: {
		CustomTabbar
	},
	data() {
		return {
			currentCategory: 0,
			hasMore: true,
			categories: [
				{ icon: '🛍️', name: '全部' },
				{ icon: '👕', name: '服装' },
				{ icon: '📱', name: '数码' },
				{ icon: '🏠', name: '家居' },
				{ icon: '🍔', name: '食品' },
				{ icon: '💄', name: '美妆' },
				{ icon: '📚', name: '图书' },
				{ icon: '🎮', name: '玩具' }
			],
			productList: [
				{
					id: 1,
					name: '夏季清仓T恤',
					image: '/static/images/product1.jpg',
					currentPrice: '29.9',
					originalPrice: '89.9',
					discount: '3.3折',
					tag: '清仓',
					stock: 15
				},
				{
					id: 2,
					name: '库存处理手机壳',
					image: '/static/images/product2.jpg',
					currentPrice: '9.9',
					originalPrice: '39.9',
					discount: '2.5折',
					tag: '特价',
					stock: 8
				},
				{
					id: 3,
					name: '家居用品套装',
					image: '/static/images/product3.jpg',
					currentPrice: '59.9',
					originalPrice: '159.9',
					discount: '3.7折',
					tag: '限时',
					stock: 23
				}
			]
		}
	},
	onLoad() {
		uni.setNavigationBarTitle({
			title: '特价清仓区'
		});
	},
	onShow() {
		// 同步tabBar状态
		this.$nextTick(() => {
			if (this.$refs.customTabbar) {
				this.$refs.customTabbar.syncCurrentPageState();
			}
		});
	},
	methods: {
		// 切换分类
		switchCategory(index) {
			this.currentCategory = index;
			// 这里可以根据分类加载不同的商品数据
		},

		// 获取图片URL
		getImageUrl(imagePath) {
			if (!imagePath) return '/static/images/placeholder.jpg';
			if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
				return imagePath;
			}
			return '/static/images/' + imagePath;
		},

		// 跳转到商品详情
		goToProductDetail(product) {
			uni.navigateTo({
				url: `/pages/product/detail?id=${product.id}`
			});
		},

		// tabBar切换事件
		onTabChange() {
			// 自定义tabBar组件内部已经处理了页面跳转
		}
	}
}
</script>

<style lang="scss" scoped>
.clearance-container {
	min-height: 100vh;
	background: linear-gradient(to bottom, #ff6b6b, #ffffff 30%);
	padding-bottom: 120rpx; /* 为自定义tabBar预留空间 */
}

.header {
	padding: 40rpx 30rpx 20rpx;
	text-align: center;

	.header-content {
		.title {
			display: block;
			font-size: 48rpx;
			font-weight: bold;
			color: #fff;
			text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
			margin-bottom: 10rpx;
		}

		.subtitle {
			display: block;
			font-size: 28rpx;
			color: rgba(255, 255, 255, 0.9);
		}
	}
}

.banner-section {
	margin: 20rpx;
	border-radius: 16rpx;
	overflow: hidden;
	box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);

	.banner-swiper {
		height: 300rpx;
	}

	.banner-item {
		height: 100%;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		color: #fff;
		position: relative;

		&.banner-1 {
			background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		}

		&.banner-2 {
			background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
		}

		&.banner-3 {
			background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
		}

		.banner-text {
			font-size: 36rpx;
			font-weight: bold;
			margin-bottom: 10rpx;
		}

		.banner-desc {
			font-size: 24rpx;
			opacity: 0.9;
		}
	}
}

.category-tabs {
	margin: 20rpx 0;

	.tabs-scroll {
		white-space: nowrap;
		padding: 0 20rpx;
	}

	.tab-item {
		display: inline-block;
		margin-right: 20rpx;
		padding: 16rpx 24rpx;
		background: rgba(255, 255, 255, 0.9);
		border-radius: 50rpx;
		text-align: center;
		transition: all 0.3s ease;

		&.active {
			background: #ff6b6b;
			color: #fff;
		}

		.tab-icon {
			font-size: 32rpx;
			margin-right: 8rpx;
		}

		.tab-text {
			font-size: 24rpx;
		}
	}
}

.product-list {
	padding: 0 20rpx;
}

.product-item {
	display: flex;
	background: #fff;
	border-radius: 16rpx;
	margin-bottom: 20rpx;
	padding: 20rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.98);
	}

	.product-image {
		position: relative;
		width: 200rpx;
		height: 200rpx;
		border-radius: 12rpx;
		overflow: hidden;
		margin-right: 20rpx;

		image {
			width: 100%;
			height: 100%;
		}

		.discount-tag {
			position: absolute;
			top: 10rpx;
			left: 10rpx;
			background: #ff4757;
			color: #fff;
			padding: 4rpx 12rpx;
			border-radius: 20rpx;
			font-size: 20rpx;
			font-weight: bold;
		}
	}

	.product-info {
		flex: 1;
		display: flex;
		flex-direction: column;
		justify-content: space-between;

		.product-name {
			font-size: 32rpx;
			font-weight: 600;
			color: #333;
			margin-bottom: 20rpx;
			line-height: 1.4;
		}

		.price-section {
			margin-bottom: 20rpx;

			.current-price {
				font-size: 36rpx;
				font-weight: bold;
				color: #ff4757;
				margin-right: 20rpx;
			}

			.original-price {
				font-size: 24rpx;
				color: #999;
				text-decoration: line-through;
			}
		}

		.product-tags {
			display: flex;
			justify-content: space-between;
			align-items: center;

			.tag {
				background: #ff6b6b;
				color: #fff;
				padding: 6rpx 16rpx;
				border-radius: 20rpx;
				font-size: 20rpx;
			}

			.stock {
				font-size: 24rpx;
				color: #ff4757;
				font-weight: 600;
			}
		}
	}
}

.load-more, .no-more {
	text-align: center;
	padding: 40rpx;
	color: #999;
	font-size: 28rpx;
}
</style>
