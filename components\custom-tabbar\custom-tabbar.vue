<template>
	<view class="custom-tabbar" :style="{ paddingBottom: safeAreaInsets.bottom + 'px' }">
		<!-- 背景装饰 -->
		<view class="tabbar-bg"></view>
		
		<!-- 导航项容器 -->
		<view class="tabbar-content">
			<!-- 左侧两个导航项 -->
			<view class="tabbar-item"
				  v-for="(item, index) in leftItems"
				  :key="index"
				  @click="switchLeftTab(index)">
				<view class="item-icon" :class="{ 'active': currentIndex === index }">
					<image :src="currentIndex === index ? item.selectedIconPath : item.iconPath"
						   mode="aspectFit"></image>
				</view>
				<text class="item-text" :class="{ 'active': currentIndex === index }">{{ item.text }}</text>
			</view>
			
			<!-- 中间特殊按钮 -->
			<view class="center-button" @click="switchCenterTab()">
				<view class="center-button-bg">
					<view class="center-button-inner">
						<view class="center-icon">
							<image :src="currentIndex === 2 ? centerItem.selectedIconPath : centerItem.iconPath" 
								   mode="aspectFit"></image>
						</view>
						<text class="center-text" :class="{ 'active': currentIndex === 2 }">{{ centerItem.text }}</text>
					</view>
				</view>
				<!-- 装饰光环 -->
				<view class="center-glow" :class="{ 'active': currentIndex === 2 }"></view>
			</view>
			
			<!-- 右侧两个导航项 -->
			<view class="tabbar-item"
				  v-for="(item, index) in rightItems"
				  :key="item.pagePath"
				  @click="switchRightTab(index)">
				<view class="item-icon" :class="{ 'active': currentIndex === (index + 3) }">
					<image :src="currentIndex === (index + 3) ? item.selectedIconPath : item.iconPath"
						   mode="aspectFit"></image>
				</view>
				<text class="item-text" :class="{ 'active': currentIndex === (index + 3) }">{{ item.text }}</text>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'CustomTabbar',
	props: {
		current: {
			type: Number,
			default: 2
		}
	},
	data() {
		return {
			currentIndex: 2,
			switching: false, // 防止快速连续点击的标志
			safeAreaInsets: { bottom: 0 },
			// 左侧导航项
			leftItems: [
				{
					pagePath: '/pages/clearance/index',
					iconPath: '/static/images/tabbar/buy-group.png.png',
					selectedIconPath: '/static/images/tabbar/buy-group.png_.png',
					text: '特价区'
				},
				{
					pagePath: '/pages/friend/index',
					iconPath: '/static/images/tabbar/friend.png',
					selectedIconPath: '/static/images/tabbar/friend_.png',
					text: '商友'
				}
			],
			// 中间特殊按钮
			centerItem: {
				pagePath: '/pages/index',
				iconPath: '/static/images/tabbar/home.png',
				selectedIconPath: '/static/images/tabbar/home_.png',
				text: '找货'
			},
			// 右侧导航项
			rightItems: [
				{
					pagePath: '/pages/work/index',
					iconPath: '/static/images/tabbar/work.png',
					selectedIconPath: '/static/images/tabbar/work_.png',
					text: '访客/动态'
				},
				{
					pagePath: '/pages/mine/index',
					iconPath: '/static/images/tabbar/mine.png',
					selectedIconPath: '/static/images/tabbar/mine_.png',
					text: '我的'
				}
			]
		}
	},
	mounted() {
		// 获取系统信息
		this.getSystemInfo();
		// 验证数据完整性
		this.validateTabData();
		// 同步当前页面状态
		this.syncCurrentPageState();
	},
	watch: {
		current: {
			handler(newVal) {
				this.currentIndex = newVal;
			},
			immediate: true
		}
	},
	methods: {
		// 获取系统信息
		getSystemInfo() {
			// 使用新的API获取窗口信息
			if (uni.getWindowInfo) {
				const windowInfo = uni.getWindowInfo();
				this.safeAreaInsets = windowInfo.safeAreaInsets || { bottom: 0 };
			} else {
				// 兼容旧版本
				const systemInfo = uni.getSystemInfoSync();
				this.safeAreaInsets = systemInfo.safeAreaInsets || { bottom: 0 };
			}
		},

		switchTab(pagePath, index) {
			// 防止重复点击
			if (this.currentIndex === index) {
				return;
			}

			// 验证pagePath
			if (!pagePath) {
				return;
			}

			// 防止快速连续点击
			if (this.switching) {
				return;
			}

			this.switching = true;

			uni.switchTab({
				url: pagePath,
				success: () => {
					this.switching = false;
					// 不在这里更新状态，让页面的onShow来同步状态
				},
				fail: () => {
					this.switching = false;
				}
			});
		},

		// 专门处理左侧导航项的点击
		switchLeftTab(index) {
			if (this.switching) {
				return;
			}

			const item = this.leftItems[index];

			if (!item || !item.pagePath) {
				return;
			}

			// 确保索引正确
			if (index < 0 || index >= this.leftItems.length) {
				return;
			}

			this.switchTab(item.pagePath, index);
		},

		// 专门处理中间按钮的点击
		switchCenterTab() {
			if (this.switching) {
				return;
			}

			if (!this.centerItem || !this.centerItem.pagePath) {
				return;
			}

			this.switchTab(this.centerItem.pagePath, 2);
		},

		// 专门处理右侧导航项的点击
		switchRightTab(index) {
			if (this.switching) {
				return;
			}

			const realIndex = index + 3; // 右侧导航项的实际索引
			const item = this.rightItems[index];

			if (!item || !item.pagePath) {
				return;
			}

			// 确保索引正确
			if (index < 0 || index >= this.rightItems.length) {
				return;
			}

			this.switchTab(item.pagePath, realIndex);
		},

		// 验证tabBar数据完整性
		validateTabData() {
			// 验证左侧导航项
			this.leftItems.forEach((item) => {
				if (!item.pagePath) {
					// 数据验证失败
				}
			});

			// 验证中间按钮
			if (!this.centerItem.pagePath) {
				// 数据验证失败
			}

			// 验证右侧导航项
			this.rightItems.forEach((item) => {
				if (!item.pagePath) {
					// 数据验证失败
				}
			});
		},

		// 同步当前页面状态
		syncCurrentPageState() {
			const pages = getCurrentPages();
			if (pages.length === 0) return;

			const currentPage = pages[pages.length - 1];
			const currentRoute = '/' + currentPage.route;

			// 根据当前路由确定应该选中的tab
			let targetIndex = -1;

			// 检查左侧导航项
			this.leftItems.forEach((item, index) => {
				if (item.pagePath === currentRoute) {
					targetIndex = index;
				}
			});

			// 检查中间按钮
			if (this.centerItem.pagePath === currentRoute) {
				targetIndex = 2;
			}

			// 检查右侧导航项
			this.rightItems.forEach((item, index) => {
				if (item.pagePath === currentRoute) {
					targetIndex = index + 3;
				}
			});

			if (targetIndex !== -1 && targetIndex !== this.currentIndex) {
				this.currentIndex = targetIndex;
				this.$emit('change', { index: targetIndex, pagePath: currentRoute });
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.custom-tabbar {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	z-index: 1000;
	background: transparent;
	
	.tabbar-bg {
		position: absolute;
		bottom: 0;
		left: 0;
		right: 0;
		height: 120rpx;
		background: linear-gradient(180deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.98) 100%);
		backdrop-filter: blur(20rpx);
		border-top: 1rpx solid rgba(0, 0, 0, 0.05);
	}
	
	.tabbar-content {
		position: relative;
		display: flex;
		align-items: flex-end;
		height: 120rpx;
		padding: 0 20rpx;
	}
	
	.tabbar-item {
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 10rpx 0;
		transition: all 0.3s ease;

		.item-icon {
			width: 48rpx;
			height: 48rpx;
			margin-bottom: 6rpx;
			transition: all 0.3s ease;

			image {
				width: 100%;
				height: 100%;
				transition: all 0.3s ease;
			}

			// 选中状态的图标颜色过滤器，使其与文字颜色一致 (#fa3534)
			&.active image {
				filter: brightness(0) saturate(100%) invert(27%) sepia(51%) saturate(2878%) hue-rotate(346deg) brightness(104%) contrast(97%);
			}
		}

		.item-text {
			font-size: 20rpx;
			color: #999;
			transition: all 0.3s ease;

			&.active {
				color: #fa3534;
				font-weight: 600;
			}
		}

		&:active {
			transform: scale(0.95);
		}
	}
	
	.center-button {
		position: relative;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		width: 120rpx;
		height: 120rpx;
		margin: 0 20rpx;
		margin-bottom: 20rpx;
		
		.center-button-bg {
			position: relative;
			width: 100rpx;
			height: 100rpx;
			border-radius: 50%;
			background: linear-gradient(135deg, #ff6b6b 0%, #fa3534 50%, #e91e63 100%);
			box-shadow: 0 8rpx 24rpx rgba(250, 53, 52, 0.4);
			display: flex;
			align-items: center;
			justify-content: center;
			transition: all 0.3s ease;
			
			&::before {
				content: '';
				position: absolute;
				top: -4rpx;
				left: -4rpx;
				right: -4rpx;
				bottom: -4rpx;
				border-radius: 50%;
				background: linear-gradient(135deg, #ff6b6b, #fa3534, #e91e63);
				opacity: 0.3;
				z-index: -1;
			}
		}
		
		.center-button-inner {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
		}
		
		.center-icon {
			width: 56rpx;
			height: 56rpx;
			margin-bottom: 4rpx;
			filter: brightness(0) invert(1);
			
			image {
				width: 100%;
				height: 100%;
			}
		}
		
		.center-text {
			font-size: 20rpx;
			color: #fff;
			font-weight: 600;
			text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
		}
		
		.center-glow {
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
			width: 140rpx;
			height: 140rpx;
			border-radius: 50%;
			background: radial-gradient(circle, rgba(250, 53, 52, 0.2) 0%, transparent 70%);
			opacity: 0;
			transition: all 0.6s ease;
			z-index: -1;
			
			&.active {
				opacity: 1;
				animation: pulse 2s infinite;
			}
		}
		
		&:active {
			transform: scale(0.95);
			
			.center-button-bg {
				box-shadow: 0 4rpx 16rpx rgba(250, 53, 52, 0.6);
			}
		}
	}
}

@keyframes pulse {
	0% {
		transform: translate(-50%, -50%) scale(1);
		opacity: 0.6;
	}
	50% {
		transform: translate(-50%, -50%) scale(1.1);
		opacity: 0.3;
	}
	100% {
		transform: translate(-50%, -50%) scale(1);
		opacity: 0.6;
	}
}

// 适配不同设备的安全区域
@supports (bottom: env(safe-area-inset-bottom)) {
	.custom-tabbar {
		padding-bottom: env(safe-area-inset-bottom);
	}
}
</style>
