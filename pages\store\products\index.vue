<template>
	<view class="page-container">
		<!-- 顶部操作栏 -->
		<view class="header-section">
			<view class="search-container">
				<u-search 
					v-model="searchForm.productName" 
					placeholder="搜索商品名称" 
					@search="handleSearch"
					@clear="handleClear"
					:show-action="false"
					bg-color="#f8f9fa"
				></u-search>
			</view>
			<view class="action-buttons">
				<u-button type="primary" size="small" @click="goToAdd">
					<u-icon name="plus" size="14" style="margin-right: 4px;"></u-icon>
					新增商品
				</u-button>
				<u-button type="warning" size="small" plain @click="goToCategory" style="margin-left: 8px;">
					<u-icon name="list" size="14" style="margin-right: 4px;"></u-icon>
					分类管理
				</u-button>
			</view>
		</view>

		<!-- 商品列表 -->
		<view class="product-list">
			<view v-if="productList.length > 0">
				<view 
					v-for="(item, index) in productList" 
					:key="item.productId" 
					class="product-item"
					@click="goToEdit(item)"
				>
					<view class="product-image">
						<u-image 
							:src="getFirstImage(item.productImages)" 
							width="80px" 
							height="80px" 
							border-radius="8px"
							:fade="true"
							:loading-icon="true"
							error-icon="photo"
						></u-image>
						<view v-if="item.isRecommended === '1'" class="recommend-badge">
							<u-icon name="star-fill" color="#fff" size="12"></u-icon>
						</view>
					</view>
					<view class="product-info">
						<view class="product-name">{{ item.productName }}</view>
						<view class="product-category">分类：{{ item.categoryName || '未分类' }}</view>
						<view class="product-switches" @click.stop="">
							<view class="switch-item">
								<text class="switch-label">促销</text>
								<u-switch
									v-model="item.isPromotion"
									:value="item.isPromotion === '1'"
									@change="handlePromotionChange(item, $event)"
									size="20"
									active-color="#fa3534"
								></u-switch>
							</view>
						</view>
						<view class="product-meta">
							<text class="create-time">{{ formatTime(item.createTime) }}</text>
							<view class="product-actions" @click.stop="">
								<u-button
									type="primary"
									size="mini"
									plain
									@click="goToEdit(item)"
								>编辑</u-button>
								<u-button
									type="error"
									size="mini"
									plain
									@click="handleDelete(item, index)"
									style="margin-left: 8px;"
								>删除</u-button>
							</view>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 空状态 -->
			<view v-else-if="!loading" class="empty-container">
				<u-empty
					mode="list"
					text="暂无商品数据"
					icon="http://cdn.uviewui.com/uview/empty/list.png"
				>
					<u-button 
						type="primary" 
						text="添加商品" 
						@click="goToAdd"
						custom-style="margin-top: 20px"
					></u-button>
				</u-empty>
			</view>
		</view>

		<!-- 加载更多 -->
		<u-loadmore 
			v-if="productList.length > 0"
			:status="loadmoreStatus" 
			@loadmore="loadMore"
		></u-loadmore>
	</view>
</template>

<script>
	import { listProduct, delProduct,updateProduct } from "@/api/buy/product.js";
	import { listProductCategory } from "@/api/buy/productCategory.js";
	import { listStoreInfo } from "@/api/buy/storeInfo.js";
	import { baseUrl } from "@/config.js";

	export default {
		data() {
			return {
				userId: this.$store.state.user.userId,
				storeId: null,
				baseUrl,
				loading: false,
				searchForm: {
					productName: ''
				},
				queryParams: {
					pageNum: 1,
					pageSize: 10,
					storeId: null,
					productName: ''
				},
				productList: [],
				categoryList: [],
				total: 0,
				loadmoreStatus: 'loadmore'
			}
		},
		onLoad() {
			this.getStoreInfo();
		},
		onShow() {
			// 从新增/编辑页面返回时刷新列表
			if (this.storeId) {
				this.refreshList();
			}
		},
		methods: {
			// 获取店铺信息
			async getStoreInfo() {
				try {
					this.$modal.loading('加载中...');
					const res = await listStoreInfo({ userId: this.userId, pageNum: 1, pageSize: 1 });
					this.$modal.closeLoading();
					
					if (res.code === 200 && res.rows.length > 0) {
						this.storeId = res.rows[0].storeId;
						this.queryParams.storeId = this.storeId;
						await this.getCategoryList();
						await this.getProductList();
					} else {
						this.$modal.msgError('请先完成店铺入驻');
						setTimeout(() => {
							uni.navigateBack();
						}, 1500);
					}
				} catch (error) {
					this.$modal.closeLoading();
					this.$modal.msgError('获取店铺信息失败');
				}
			},

			// 获取分类列表
			async getCategoryList() {
				try {
					const res = await listProductCategory({ 
						storeId: this.storeId, 
						pageSize: 1000 
					});
					if (res.code === 200) {
						this.categoryList = res.rows || [];
					}
				} catch (error) {
					console.error('获取分类列表失败:', error);
				}
			},

			// 获取商品列表
			async getProductList(isLoadMore = false) {
				if (this.loading) return;
				
				this.loading = true;
				if (!isLoadMore) {
					this.loadmoreStatus = 'loading';
				}

				try {
					const res = await listProduct(this.queryParams);
					this.loading = false;

					if (res.code === 200) {
						const newList = res.rows || [];
						
						// 为商品添加分类名称
						newList.forEach(product => {
							const category = this.categoryList.find(cat => cat.categoryId === product.categoryId);
							product.categoryName = category ? category.categoryName : '';
						});

						if (isLoadMore) {
							this.productList = [...this.productList, ...newList];
						} else {
							this.productList = newList;
						}
						
						this.total = res.total || 0;
						
						// 更新加载更多状态
						if (this.productList.length >= this.total) {
							this.loadmoreStatus = 'nomore';
						} else {
							this.loadmoreStatus = 'loadmore';
						}
					} else {
						this.$modal.msgError(res.msg || '获取商品列表失败');
						this.loadmoreStatus = 'loadmore';
					}
				} catch (error) {
					this.loading = false;
					this.loadmoreStatus = 'loadmore';
					this.$modal.msgError('获取商品列表失败');
				}
			},

			// 搜索
			handleSearch() {
				this.queryParams.productName = this.searchForm.productName;
				this.queryParams.pageNum = 1;
				this.getProductList();
			},

			// 清空搜索
			handleClear() {
				this.searchForm.productName = '';
				this.queryParams.productName = '';
				this.queryParams.pageNum = 1;
				this.getProductList();
			},

			// 加载更多
			loadMore() {
				if (this.loadmoreStatus === 'loadmore') {
					this.queryParams.pageNum++;
					this.getProductList(true);
				}
			},

			// 刷新列表
			refreshList() {
				this.queryParams.pageNum = 1;
				this.getProductList();
			},

			// 获取第一张图片
			getFirstImage(images) {
				if (!images) return '/static/images/default-product.png';
				
				const imageList = images.split(',');
				const firstImage = imageList[0];
				
				// 如果不是完整URL，拼接baseUrl
				if (firstImage && !firstImage.startsWith('http://') && !firstImage.startsWith('https://')) {
					return this.baseUrl + firstImage;
				}
				return firstImage || '/static/images/default-product.png';
			},

			// 格式化时间
			formatTime(time) {
				if (!time) return '';
				const date = new Date(time);
				return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
			},

			// 跳转到新增页面
			goToAdd() {
				uni.navigateTo({
					url: '/pages/store/products/form'
				});
			},

			// 跳转到编辑页面
			goToEdit(item) {
				uni.navigateTo({
					url: `/pages/store/products/form?id=${item.productId}`
				});
			},

			// 跳转到分类管理
			goToCategory() {
				uni.navigateTo({
					url: '/pages/store/category/index'
				});
			},

			// 处理促销开关变化
			async handlePromotionChange(item, value) {
				try {
					this.$modal.loading('更新中...');

					// 准备更新数据
					const updateData = {
						productId: item.productId,
						isPromotion: value ? '1' : '0'
					};

					const result = await updateProduct(updateData);
					this.$modal.closeLoading();

					if (result.code === 200) {
						// 更新本地数据
						item.isPromotion = value ? '1' : '0';
						this.$modal.msgSuccess(value ? '已设为促销商品' : '已取消促销');
					} else {
						// 更新失败，恢复开关状态
						item.isPromotion = item.isPromotion === '1' ? '0' : '1';
						this.$modal.msgError(result.msg || '更新失败');
					}
				} catch (error) {
					this.$modal.closeLoading();
					// 更新失败，恢复开关状态
					item.isPromotion = item.isPromotion === '1' ? '0' : '1';
					this.$modal.msgError('更新失败');
				}
			},

			// 删除商品
			handleDelete(item, index) {
				uni.showModal({
					title: '确认删除',
					content: `确定要删除商品"${item.productName}"吗？`,
					success: async (res) => {
						if (res.confirm) {
							try {
								this.$modal.loading('删除中...');
								const result = await delProduct(item.productId);
								this.$modal.closeLoading();
								
								if (result.code === 200) {
									this.$modal.msgSuccess('删除成功');
									this.productList.splice(index, 1);
									this.total--;
								} else {
									this.$modal.msgError(result.msg || '删除失败');
								}
							} catch (error) {
								this.$modal.closeLoading();
								this.$modal.msgError('删除失败');
							}
						}
					}
				});
			}
		}
	}
</script>

<style lang="scss">
	.page-container {
		background-color: #f5f5f5;
		min-height: 100vh;
	}

	.header-section {
		background-color: #fff;
		padding: 15px;
		border-bottom: 1px solid #f0f0f0;
		
		.search-container {
			margin-bottom: 12px;
		}
		
		.action-buttons {
			display: flex;
			justify-content: flex-end;
		}
	}

	.product-list {
		padding: 0 15px;
	}

	.product-item {
		background-color: #fff;
		border-radius: 8px;
		padding: 15px;
		margin: 12px 0;
		display: flex;
		box-shadow: 0 2px 12px 0 rgba(0,0,0,0.06);
		
		.product-image {
			position: relative;
			margin-right: 15px;
			
			.recommend-badge {
				position: absolute;
				top: -5px;
				right: -5px;
				background: linear-gradient(45deg, #ff6b6b, #ffa500);
				border-radius: 50%;
				width: 20px;
				height: 20px;
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}
		
		.product-info {
			flex: 1;
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			
			.product-name {
				font-size: 16px;
				font-weight: 600;
				color: #303133;
				margin-bottom: 8px;
				line-height: 1.4;
			}
			
			.product-category {
				font-size: 13px;
				color: #909399;
				margin-bottom: 8px;
			}

			.product-switches {
				margin-bottom: 8px;

				.switch-item {
					display: flex;
					align-items: center;

					.switch-label {
						font-size: 14px;
						color: #606266;
						margin-right: 10px;
						min-width: 40px;
					}
				}
			}

			.product-meta {
				display: flex;
				justify-content: space-between;
				align-items: center;
				
				.create-time {
					font-size: 12px;
					color: #c0c4cc;
				}
				
				.product-actions {
					display: flex;
				}
			}
		}
	}

	.empty-container {
		padding-top: 100px;
	}
</style>
